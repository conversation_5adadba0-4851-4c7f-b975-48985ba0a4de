<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Follow Bubble</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .test-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 15px;
            color: white;
            text-align: center;
            z-index: 5000;
        }
        
        .test-controls button {
            margin: 5px;
            padding: 10px 20px;
            background: #ff6b9d;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-controls button:hover {
            background: #ff5588;
        }
    </style>
</head>
<body>
    <div id="chat-container"></div>
    
    <!-- Hidden settings elements for script compatibility -->
    <input type="range" id="bubble-duration" min="3" max="15" value="8" style="display: none;">
    <span id="duration-value" style="display: none;">8s</span>
    <input type="range" id="max-bubbles" min="5" max="20" value="10" style="display: none;">
    <span id="max-bubbles-value" style="display: none;">10</span>
    <select id="bubble-size" style="display: none;">
        <option value="small">Small</option>
        <option value="medium" selected>Medium</option>
        <option value="large">Large</option>
    </select>
    <select id="animation-speed" style="display: none;">
        <option value="slow">Slow</option>
        <option value="medium" selected>Medium</option>
        <option value="fast">Fast</option>
    </select>
    
    <div class="test-controls">
        <h3>🧪 Follow Bubble Test</h3>
        <button onclick="testChat()">Chat Bubble</button>
        <button onclick="testFollow()">Follow Bubble</button>
        <button onclick="testBoth()">Both Types</button>
        <button onclick="clearAll()">Clear All</button>
        <p style="margin-top: 10px; font-size: 12px;">
            Press <strong>T</strong> for Chat | Press <strong>F</strong> for Follow
        </p>
    </div>
    
    <script src="script.js"></script>
    <script>
        function testChat() {
            chatManager.testBubble();
        }
        
        function testFollow() {
            chatManager.testFollowBubble();
        }
        
        function testBoth() {
            chatManager.testBubble();
            setTimeout(() => {
                chatManager.testFollowBubble();
            }, 1000);
        }
        
        function clearAll() {
            chatManager.activeBubbles.forEach(bubble => {
                if (bubble && bubble.parentNode) {
                    bubble.remove();
                }
            });
            chatManager.activeBubbles = [];
        }
        
        // Auto test on load
        setTimeout(() => {
            testFollow();
        }, 1000);
        
        setTimeout(() => {
            testChat();
        }, 2000);
    </script>
</body>
</html>
