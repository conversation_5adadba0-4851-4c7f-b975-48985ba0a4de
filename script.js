class ChatBubbleManager {
    constructor() {
        this.ws = null;
        this.chatContainer = document.getElementById('chat-container');
        this.activeBubbles = [];
        this.colorIndex = 0;
        
        // Settings
        this.settings = {
            bubbleDuration: 12,
            maxBubbles: 10,
            bubbleSize: 'medium',
            animationSpeed: 'medium'
        };
        
        this.initializeSettings();
        this.connectWebSocket();
        this.setupEventListeners();
    }
    
    initializeSettings() {
        const durationSlider = document.getElementById('bubble-duration');
        const maxBubblesSlider = document.getElementById('max-bubbles');
        const bubbleSizeSelect = document.getElementById('bubble-size');
        const animationSpeedSelect = document.getElementById('animation-speed');
        
        // Update display values
        const updateDurationValue = () => {
            document.getElementById('duration-value').textContent = durationSlider.value + 's';
            this.settings.bubbleDuration = parseInt(durationSlider.value);
        };
        
        const updateMaxBubblesValue = () => {
            document.getElementById('max-bubbles-value').textContent = maxBubblesSlider.value;
            this.settings.maxBubbles = parseInt(maxBubblesSlider.value);
        };
        
        durationSlider.addEventListener('input', updateDurationValue);
        maxBubblesSlider.addEventListener('input', updateMaxBubblesValue);
        
        bubbleSizeSelect.addEventListener('change', (e) => {
            this.settings.bubbleSize = e.target.value;
        });
        
        animationSpeedSelect.addEventListener('change', (e) => {
            this.settings.animationSpeed = e.target.value;
        });
        
        // Initialize values
        updateDurationValue();
        updateMaxBubblesValue();
    }
    
    setupEventListeners() {
        // Settings functionality removed for clean OBS overlay
    }
    
    connectWebSocket() {
        try {
            this.ws = new WebSocket('ws://localhost:62024');
            
            this.ws.onopen = () => {
                console.log('Terhubung ke IndoFinity WebSocket');
            };
            
            this.ws.onmessage = (data) => {
                try {
                    const message = JSON.parse(data.data);
                    const { event, data: eventData } = message;
                    
                    console.log(`Event: ${event}`);
                    console.log('Data:', eventData);
                    
                    if (event === 'chat') {
                        this.createChatBubble(eventData.uniqueId, eventData.comment);
                    } else if (event === 'follow') {
                        this.createFollowBubble(eventData.uniqueId);
                    } else if (event === 'share') {
                        this.createShareBubble(eventData.uniqueId);
                    } else if (event === 'like') {
                        this.createLikeBubble(eventData.uniqueId);
                    }
                } catch (error) {
                    console.error('Error parsing message:', error);
                }
            };
            
            this.ws.onclose = () => {
                console.log('Koneksi WebSocket ditutup');
                // Attempt to reconnect after 3 seconds
                setTimeout(() => this.connectWebSocket(), 3000);
            };

            this.ws.onerror = (err) => {
                console.error('WebSocket error:', err);
            };
            
        } catch (error) {
            console.error('Failed to connect to WebSocket:', error);
            // Attempt to reconnect after 5 seconds
            setTimeout(() => this.connectWebSocket(), 5000);
        }
    }
    
    createChatBubble(username, message) {
        // Remove oldest bubbles if we exceed max
        while (this.activeBubbles.length >= this.settings.maxBubbles) {
            const oldBubble = this.activeBubbles.shift();
            if (oldBubble && oldBubble.parentNode) {
                oldBubble.remove();
            }
        }

        const bubble = document.createElement('div');
        bubble.className = `chat-bubble color-${(this.colorIndex % 8) + 1} ${this.settings.bubbleSize} ${this.settings.animationSpeed}`;

        // Get first letter of username for avatar
        const avatarLetter = username.charAt(0).toUpperCase();

        // Random decorations
        const decorations = ['🌸', '✨', '💫', '🌟', '💖', '🎀', '🦋', '🌺'];
        const randomDecoration = decorations[Math.floor(Math.random() * decorations.length)];

        bubble.innerHTML = `
            <div class="avatar">${avatarLetter}</div>
            <div class="content">
                <div class="username">${this.escapeHtml(username)}</div>
                <div class="message">${this.escapeHtml(message)}</div>
            </div>
            <div class="decorations">${randomDecoration}</div>
        `;

        // Random horizontal position
        const randomX = Math.random() * (window.innerWidth - 400); // Account for bubble width
        bubble.style.left = randomX + 'px';

        // Set animation duration based on settings
        const duration = this.settings.bubbleDuration;
        bubble.style.animationDuration = duration + 's';

        this.chatContainer.appendChild(bubble);
        this.activeBubbles.push(bubble);

        // Remove bubble after animation completes
        setTimeout(() => {
            if (bubble.parentNode) {
                bubble.remove();
                const index = this.activeBubbles.indexOf(bubble);
                if (index > -1) {
                    this.activeBubbles.splice(index, 1);
                }
            }
        }, duration * 1000);

        this.colorIndex++;
    }

    createFollowBubble(username) {
        // Remove oldest bubbles if we exceed max
        while (this.activeBubbles.length >= this.settings.maxBubbles) {
            const oldBubble = this.activeBubbles.shift();
            if (oldBubble && oldBubble.parentNode) {
                oldBubble.remove();
            }
        }

        const bubble = document.createElement('div');
        bubble.className = `chat-bubble follow-bubble color-${(this.colorIndex % 8) + 1} ${this.settings.bubbleSize} ${this.settings.animationSpeed}`;

        // Get first letter of username for avatar
        const avatarLetter = username.charAt(0).toUpperCase();

        // Follow-specific decorations
        const followDecorations = ['❤️', '🎉', '👑', '⭐', '💖', '🌟', '✨', '🎊'];
        const randomDecoration = followDecorations[Math.floor(Math.random() * followDecorations.length)];

        bubble.innerHTML = `
            <div class="avatar">${avatarLetter}</div>
            <div class="content">
                <div class="username">${this.escapeHtml(username)}</div>
                <div class="message follow-message">ได้ติดตามแล้ว! 🎉</div>
            </div>
            <div class="decorations">${randomDecoration}</div>
        `;

        // Random horizontal position
        const randomX = Math.random() * (window.innerWidth - 400);
        bubble.style.left = randomX + 'px';

        // Set animation duration for follow bubbles (longer than chat)
        const duration = this.settings.bubbleDuration + 3; // Follow bubbles last 3 seconds longer
        bubble.style.animationDuration = duration + 's';

        this.chatContainer.appendChild(bubble);
        this.activeBubbles.push(bubble);

        // Remove bubble after animation completes
        setTimeout(() => {
            if (bubble.parentNode) {
                bubble.remove();
                const index = this.activeBubbles.indexOf(bubble);
                if (index > -1) {
                    this.activeBubbles.splice(index, 1);
                }
            }
        }, duration * 1000);

        this.colorIndex++;
    }

    createShareBubble(username) {
        // Remove oldest bubbles if we exceed max
        while (this.activeBubbles.length >= this.settings.maxBubbles) {
            const oldBubble = this.activeBubbles.shift();
            if (oldBubble && oldBubble.parentNode) {
                oldBubble.remove();
            }
        }

        const bubble = document.createElement('div');
        bubble.className = `chat-bubble share-bubble color-${(this.colorIndex % 8) + 1} ${this.settings.bubbleSize} ${this.settings.animationSpeed}`;

        // Get first letter of username for avatar
        const avatarLetter = username.charAt(0).toUpperCase();

        // Share-specific decorations
        const shareDecorations = ['🚀', '📢', '💫', '⚡', '🌟', '✨', '🎯', '🔥'];
        const randomDecoration = shareDecorations[Math.floor(Math.random() * shareDecorations.length)];

        bubble.innerHTML = `
            <div class="avatar">${avatarLetter}</div>
            <div class="content">
                <div class="username">${this.escapeHtml(username)}</div>
                <div class="message share-message">แชร์สตรีมแล้ว! 🚀</div>
            </div>
            <div class="decorations">${randomDecoration}</div>
        `;

        // Random horizontal position
        const randomX = Math.random() * (window.innerWidth - 400);
        bubble.style.left = randomX + 'px';

        // Set animation duration for share bubbles (same as follow)
        const duration = this.settings.bubbleDuration + 3; // Share bubbles last 3 seconds longer
        bubble.style.animationDuration = duration + 's';

        this.chatContainer.appendChild(bubble);
        this.activeBubbles.push(bubble);

        // Remove bubble after animation completes
        setTimeout(() => {
            if (bubble.parentNode) {
                bubble.remove();
                const index = this.activeBubbles.indexOf(bubble);
                if (index > -1) {
                    this.activeBubbles.splice(index, 1);
                }
            }
        }, duration * 1000);

        this.colorIndex++;
    }

    createLikeBubble(username) {
        // Remove oldest bubbles if we exceed max
        while (this.activeBubbles.length >= this.settings.maxBubbles) {
            const oldBubble = this.activeBubbles.shift();
            if (oldBubble && oldBubble.parentNode) {
                oldBubble.remove();
            }
        }

        const bubble = document.createElement('div');
        bubble.className = `chat-bubble like-bubble color-${(this.colorIndex % 8) + 1} ${this.settings.bubbleSize} ${this.settings.animationSpeed}`;

        // Get first letter of username for avatar
        const avatarLetter = username.charAt(0).toUpperCase();

        // Like-specific decorations
        const likeDecorations = ['💖', '💕', '💗', '💓', '💝', '💘', '❤️', '💜'];
        const randomDecoration = likeDecorations[Math.floor(Math.random() * likeDecorations.length)];

        bubble.innerHTML = `
            <div class="avatar">${avatarLetter}</div>
            <div class="content">
                <div class="username">${this.escapeHtml(username)}</div>
                <div class="message like-message">กดหัวใจแล้ว! 💖</div>
            </div>
            <div class="decorations">${randomDecoration}</div>
        `;

        // Random horizontal position
        const randomX = Math.random() * (window.innerWidth - 400);
        bubble.style.left = randomX + 'px';

        // Set animation duration for like bubbles (shorter than follow/share)
        const duration = this.settings.bubbleDuration + 1; // Like bubbles last 1 second longer than chat
        bubble.style.animationDuration = duration + 's';

        this.chatContainer.appendChild(bubble);
        this.activeBubbles.push(bubble);

        // Remove bubble after animation completes
        setTimeout(() => {
            if (bubble.parentNode) {
                bubble.remove();
                const index = this.activeBubbles.indexOf(bubble);
                if (index > -1) {
                    this.activeBubbles.splice(index, 1);
                }
            }
        }, duration * 1000);

        this.colorIndex++;
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Method to test bubbles (for development)
    testBubble() {
        const testMessages = [
            { user: 'TestUser1', msg: 'Hello everyone! 👋' },
            { user: 'TestUser2', msg: 'This is a test message' },
            { user: 'TestUser3', msg: 'Amazing stream! Keep it up! 🔥' },
            { user: 'TestUser4', msg: 'LOL that was funny 😂' },
            { user: 'TestUser5', msg: 'First time here, loving it!' }
        ];

        const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
        this.createChatBubble(randomMessage.user, randomMessage.msg);
    }

    // Method to test follow bubbles (for development)
    testFollowBubble() {
        const testFollowers = [
            'NewFollower1', 'FanGirl123', 'StreamLover', 'Supporter99', 'FollowMe'
        ];

        const randomFollower = testFollowers[Math.floor(Math.random() * testFollowers.length)];
        this.createFollowBubble(randomFollower);
    }

    // Method to test share bubbles (for development)
    testShareBubble() {
        const testSharers = [
            'ShareMaster', 'Viral123', 'SpreadLove', 'ShareKing', 'BoostStream'
        ];

        const randomSharer = testSharers[Math.floor(Math.random() * testSharers.length)];
        this.createShareBubble(randomSharer);
    }

    // Method to test like bubbles (for development)
    testLikeBubble() {
        const testLikers = [
            'HeartLover', 'LikeKing', 'LoveStream', 'HeartFan', 'LikeMe'
        ];

        const randomLiker = testLikers[Math.floor(Math.random() * testLikers.length)];
        this.createLikeBubble(randomLiker);
    }
}

// Initialize the chat bubble manager
const chatManager = new ChatBubbleManager();

// Add keyboard shortcuts for testing
document.addEventListener('keydown', (e) => {
    if (e.key.toLowerCase() === 't' && !e.ctrlKey && !e.altKey) {
        chatManager.testBubble();
    } else if (e.key.toLowerCase() === 'f' && !e.ctrlKey && !e.altKey) {
        chatManager.testFollowBubble();
    } else if (e.key.toLowerCase() === 's' && !e.ctrlKey && !e.altKey) {
        chatManager.testShareBubble();
    } else if (e.key.toLowerCase() === 'l' && !e.ctrlKey && !e.altKey) {
        chatManager.testLikeBubble();
    }
});

// Expose for console testing
window.chatManager = chatManager;
