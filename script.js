class ChatBubbleManager {
    constructor() {
        this.ws = null;
        this.chatContainer = document.getElementById('chat-container');
        this.connectionStatus = document.getElementById('connection-status');
        this.activeBubbles = [];
        this.colorIndex = 0;
        
        // Settings
        this.settings = {
            bubbleDuration: 8,
            maxBubbles: 10,
            bubbleSize: 'medium',
            animationSpeed: 'medium'
        };
        
        this.initializeSettings();
        this.connectWebSocket();
        this.setupEventListeners();
    }
    
    initializeSettings() {
        const durationSlider = document.getElementById('bubble-duration');
        const maxBubblesSlider = document.getElementById('max-bubbles');
        const bubbleSizeSelect = document.getElementById('bubble-size');
        const animationSpeedSelect = document.getElementById('animation-speed');
        
        // Update display values
        const updateDurationValue = () => {
            document.getElementById('duration-value').textContent = durationSlider.value + 's';
            this.settings.bubbleDuration = parseInt(durationSlider.value);
        };
        
        const updateMaxBubblesValue = () => {
            document.getElementById('max-bubbles-value').textContent = maxBubblesSlider.value;
            this.settings.maxBubbles = parseInt(maxBubblesSlider.value);
        };
        
        durationSlider.addEventListener('input', updateDurationValue);
        maxBubblesSlider.addEventListener('input', updateMaxBubblesValue);
        
        bubbleSizeSelect.addEventListener('change', (e) => {
            this.settings.bubbleSize = e.target.value;
        });
        
        animationSpeedSelect.addEventListener('change', (e) => {
            this.settings.animationSpeed = e.target.value;
        });
        
        // Initialize values
        updateDurationValue();
        updateMaxBubblesValue();
    }
    
    setupEventListeners() {
        const settingsToggle = document.getElementById('settings-toggle');
        const settingsPanel = document.getElementById('settings-panel');
        const toggleButton = document.getElementById('toggle-settings');
        
        settingsToggle.addEventListener('click', () => {
            const isVisible = settingsPanel.style.display === 'block';
            settingsPanel.style.display = isVisible ? 'none' : 'block';
            settingsToggle.style.display = isVisible ? 'block' : 'none';
        });
        
        toggleButton.addEventListener('click', () => {
            settingsPanel.style.display = 'none';
            settingsToggle.style.display = 'block';
        });
    }
    
    connectWebSocket() {
        try {
            this.ws = new WebSocket('ws://localhost:62024');
            
            this.ws.onopen = () => {
                console.log('Terhubung ke IndoFinity WebSocket');
                this.updateConnectionStatus(true);
            };
            
            this.ws.onmessage = (data) => {
                try {
                    const message = JSON.parse(data.data);
                    const { event, data: eventData } = message;
                    
                    console.log(`Event: ${event}`);
                    console.log('Data:', eventData);
                    
                    if (event === 'chat') {
                        this.createChatBubble(eventData.uniqueId, eventData.comment);
                    }
                } catch (error) {
                    console.error('Error parsing message:', error);
                }
            };
            
            this.ws.onclose = () => {
                console.log('Koneksi WebSocket ditutup');
                this.updateConnectionStatus(false);
                // Attempt to reconnect after 3 seconds
                setTimeout(() => this.connectWebSocket(), 3000);
            };
            
            this.ws.onerror = (err) => {
                console.error('WebSocket error:', err);
                this.updateConnectionStatus(false);
            };
            
        } catch (error) {
            console.error('Failed to connect to WebSocket:', error);
            this.updateConnectionStatus(false);
            // Attempt to reconnect after 5 seconds
            setTimeout(() => this.connectWebSocket(), 5000);
        }
    }
    
    updateConnectionStatus(connected) {
        this.connectionStatus.textContent = connected ? 'Connected' : 'Disconnected';
        this.connectionStatus.className = connected ? 'connected' : 'disconnected';
    }
    
    createChatBubble(username, message) {
        // Remove oldest bubbles if we exceed max
        while (this.activeBubbles.length >= this.settings.maxBubbles) {
            const oldBubble = this.activeBubbles.shift();
            if (oldBubble && oldBubble.parentNode) {
                oldBubble.remove();
            }
        }
        
        const bubble = document.createElement('div');
        bubble.className = `chat-bubble color-${(this.colorIndex % 8) + 1} ${this.settings.bubbleSize} ${this.settings.animationSpeed}`;
        
        bubble.innerHTML = `
            <span class="username">@${username}:</span>
            <span class="message">${this.escapeHtml(message)}</span>
        `;
        
        // Random horizontal position
        const randomX = Math.random() * (window.innerWidth - 350); // Account for bubble width
        bubble.style.left = randomX + 'px';
        
        // Set animation duration based on settings
        const duration = this.settings.bubbleDuration;
        bubble.style.animationDuration = duration + 's';
        
        this.chatContainer.appendChild(bubble);
        this.activeBubbles.push(bubble);
        
        // Remove bubble after animation completes
        setTimeout(() => {
            if (bubble.parentNode) {
                bubble.remove();
                const index = this.activeBubbles.indexOf(bubble);
                if (index > -1) {
                    this.activeBubbles.splice(index, 1);
                }
            }
        }, duration * 1000);
        
        this.colorIndex++;
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    // Method to test bubbles (for development)
    testBubble() {
        const testMessages = [
            { user: 'TestUser1', msg: 'Hello everyone! 👋' },
            { user: 'TestUser2', msg: 'This is a test message' },
            { user: 'TestUser3', msg: 'Amazing stream! Keep it up! 🔥' },
            { user: 'TestUser4', msg: 'LOL that was funny 😂' },
            { user: 'TestUser5', msg: 'First time here, loving it!' }
        ];
        
        const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
        this.createChatBubble(randomMessage.user, randomMessage.msg);
    }
}

// Initialize the chat bubble manager
const chatManager = new ChatBubbleManager();

// Add keyboard shortcut for testing (T key)
document.addEventListener('keydown', (e) => {
    if (e.key.toLowerCase() === 't' && !e.ctrlKey && !e.altKey) {
        chatManager.testBubble();
    }
});

// Expose for console testing
window.chatManager = chatManager;
