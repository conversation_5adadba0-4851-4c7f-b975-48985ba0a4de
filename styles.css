* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: transparent;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    width: 100vw;
    height: 100vh;
}

#chat-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
}

.chat-bubble {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    font-size: 14px;
    font-weight: 400;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    max-width: 350px;
    word-wrap: break-word;
    animation: floatUp 8s ease-out forwards;
    z-index: 1000;
    opacity: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-bubble.small {
    font-size: 12px;
    padding: 8px 16px;
    border-radius: 20px;
}

.chat-bubble.large {
    font-size: 16px;
    padding: 16px 24px;
    border-radius: 30px;
    max-width: 400px;
}

.chat-bubble .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    color: white;
    flex-shrink: 0;
}

.chat-bubble .content {
    flex: 1;
}

.chat-bubble .username {
    font-weight: bold;
    color: #ff9ff3;
    display: block;
    font-size: 12px;
    margin-bottom: 2px;
}

.chat-bubble .message {
    display: block;
    line-height: 1.4;
}

.chat-bubble .decorations {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 20px;
    opacity: 0.9;
    animation: floatDecoration 3s ease-in-out infinite;
}

@keyframes floatDecoration {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-5px) rotate(10deg);
    }
}

/* Different avatar colors */
.chat-bubble.color-1 .avatar { background: linear-gradient(135deg, #ff6b9d, #c44569); }
.chat-bubble.color-2 .avatar { background: linear-gradient(135deg, #667eea, #764ba2); }
.chat-bubble.color-3 .avatar { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.chat-bubble.color-4 .avatar { background: linear-gradient(135deg, #43e97b, #38f9d7); }
.chat-bubble.color-5 .avatar { background: linear-gradient(135deg, #fa709a, #fee140); }
.chat-bubble.color-6 .avatar { background: linear-gradient(135deg, #a8edea, #fed6e3); }
.chat-bubble.color-7 .avatar { background: linear-gradient(135deg, #ff9a9e, #fecfef); }
.chat-bubble.color-8 .avatar { background: linear-gradient(135deg, #ffecd2, #fcb69f); }

/* Bubble background stays consistent */
.chat-bubble {
    background: rgba(0, 0, 0, 0.85);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(255, 105, 180, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.chat-bubble:hover {
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(255, 105, 180, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Animation variants */
@keyframes floatUp {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.7) rotate(-5deg);
    }
    5% {
        opacity: 0.8;
        transform: translateY(95vh) scale(0.9) rotate(-2deg);
    }
    15% {
        opacity: 1;
        transform: translateY(85vh) scale(1) rotate(0deg);
    }
    85% {
        opacity: 1;
        transform: translateY(-5vh) scale(1) rotate(1deg);
    }
    95% {
        opacity: 0.6;
        transform: translateY(-15vh) scale(0.9) rotate(2deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-25vh) scale(0.7) rotate(3deg);
    }
}

@keyframes floatUpSlow {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.8);
    }
    8% {
        opacity: 1;
        transform: translateY(95vh) scale(1);
    }
    92% {
        opacity: 1;
        transform: translateY(-5vh) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-15vh) scale(0.8);
    }
}

@keyframes floatUpFast {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.8);
    }
    15% {
        opacity: 1;
        transform: translateY(85vh) scale(1);
    }
    85% {
        opacity: 1;
        transform: translateY(-15vh) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-25vh) scale(0.8);
    }
}

.chat-bubble.slow { animation: floatUpSlow 12s ease-out forwards; }
.chat-bubble.fast { animation: floatUpFast 5s ease-out forwards; }



/* Settings Panel */
.settings-panel {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 3000;
    display: none;
    min-width: 250px;
}

.settings-panel h3 {
    margin-bottom: 15px;
    color: #ffd700;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
}

.setting-group input[type="range"] {
    width: 100%;
    margin-bottom: 5px;
}

.setting-group select {
    width: 100%;
    padding: 5px;
    border-radius: 5px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}



button {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
}

button:hover {
    background: #5a6fd8;
}
