* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: transparent;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    width: 100vw;
    height: 100vh;
}

#chat-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
}

.chat-bubble {
    position: absolute;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 300px;
    word-wrap: break-word;
    animation: floatUp 8s ease-out forwards;
    z-index: 1000;
    opacity: 0;
}

.chat-bubble.small {
    font-size: 12px;
    padding: 8px 16px;
    border-radius: 20px;
}

.chat-bubble.large {
    font-size: 16px;
    padding: 16px 24px;
    border-radius: 30px;
    max-width: 400px;
}

.chat-bubble .username {
    font-weight: bold;
    color: #ffd700;
    margin-right: 8px;
}

.chat-bubble .message {
    display: inline;
}

/* Different bubble colors */
.chat-bubble.color-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.chat-bubble.color-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
.chat-bubble.color-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
.chat-bubble.color-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
.chat-bubble.color-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
.chat-bubble.color-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }
.chat-bubble.color-7 { background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%); }
.chat-bubble.color-8 { background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); }

/* Animation variants */
@keyframes floatUp {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.8);
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(-10vh) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-20vh) scale(0.8);
    }
}

@keyframes floatUpSlow {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.8);
    }
    8% {
        opacity: 1;
        transform: translateY(95vh) scale(1);
    }
    92% {
        opacity: 1;
        transform: translateY(-5vh) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-15vh) scale(0.8);
    }
}

@keyframes floatUpFast {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.8);
    }
    15% {
        opacity: 1;
        transform: translateY(85vh) scale(1);
    }
    85% {
        opacity: 1;
        transform: translateY(-15vh) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-25vh) scale(0.8);
    }
}

.chat-bubble.slow { animation: floatUpSlow 12s ease-out forwards; }
.chat-bubble.fast { animation: floatUpFast 5s ease-out forwards; }

/* Connection Status */
#connection-status {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    z-index: 2000;
    pointer-events: none;
}

#connection-status.connected {
    background: #4caf50;
    color: white;
}

#connection-status.disconnected {
    background: #f44336;
    color: white;
}

/* Settings Panel */
.settings-panel {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 3000;
    display: none;
    min-width: 250px;
}

.settings-panel h3 {
    margin-bottom: 15px;
    color: #ffd700;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
}

.setting-group input[type="range"] {
    width: 100%;
    margin-bottom: 5px;
}

.setting-group select {
    width: 100%;
    padding: 5px;
    border-radius: 5px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.settings-toggle {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 18px;
    cursor: pointer;
    z-index: 2500;
    backdrop-filter: blur(10px);
}

.settings-toggle:hover {
    background: rgba(0, 0, 0, 0.9);
}

button {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
}

button:hover {
    background: #5a6fd8;
}
