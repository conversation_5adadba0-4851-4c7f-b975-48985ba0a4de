* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    background: transparent;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow: hidden;
    width: 100vw;
    height: 100vh;
}

#chat-container {
    position: relative;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
}

.chat-bubble {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 25px;
    padding: 12px 20px;
    color: white;
    font-size: 14px;
    font-weight: 400;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    max-width: 350px;
    word-wrap: break-word;
    animation: floatUp 12s ease-out forwards;
    z-index: 1000;
    opacity: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.chat-bubble.small {
    font-size: 12px;
    padding: 8px 16px;
    border-radius: 20px;
}

.chat-bubble.large {
    font-size: 16px;
    padding: 16px 24px;
    border-radius: 30px;
    max-width: 400px;
}

.chat-bubble .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: bold;
    color: white;
    flex-shrink: 0;
}

.chat-bubble .content {
    flex: 1;
}

.chat-bubble .username {
    font-weight: bold;
    color: #ff9ff3;
    display: block;
    font-size: 12px;
    margin-bottom: 2px;
}

.chat-bubble .message {
    display: block;
    line-height: 1.4;
}

.chat-bubble .decorations {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 20px;
    opacity: 0.9;
    animation: floatDecoration 3s ease-in-out infinite;
}

@keyframes floatDecoration {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-5px) rotate(10deg);
    }
}

/* Different avatar colors */
.chat-bubble.color-1 .avatar { background: linear-gradient(135deg, #ff6b9d, #c44569); }
.chat-bubble.color-2 .avatar { background: linear-gradient(135deg, #667eea, #764ba2); }
.chat-bubble.color-3 .avatar { background: linear-gradient(135deg, #4facfe, #00f2fe); }
.chat-bubble.color-4 .avatar { background: linear-gradient(135deg, #43e97b, #38f9d7); }
.chat-bubble.color-5 .avatar { background: linear-gradient(135deg, #fa709a, #fee140); }
.chat-bubble.color-6 .avatar { background: linear-gradient(135deg, #a8edea, #fed6e3); }
.chat-bubble.color-7 .avatar { background: linear-gradient(135deg, #ff9a9e, #fecfef); }
.chat-bubble.color-8 .avatar { background: linear-gradient(135deg, #ffecd2, #fcb69f); }

/* Bubble background stays consistent */
.chat-bubble {
    background: rgba(0, 0, 0, 0.85);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(255, 105, 180, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.chat-bubble:hover {
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.5),
        0 0 30px rgba(255, 105, 180, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Special styling for follow bubbles */
.follow-bubble {
    background: rgba(255, 20, 147, 0.15) !important;
    border: 2px solid rgba(255, 105, 180, 0.6) !important;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(255, 20, 147, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
    animation: followFloat 15s ease-out forwards !important;
}

.follow-bubble .avatar {
    background: linear-gradient(135deg, #ff1493, #ff69b4) !important;
    box-shadow: 0 0 15px rgba(255, 20, 147, 0.5);
}

.follow-bubble .username {
    color: #ff69b4 !important;
    font-weight: bold;
}

.follow-bubble .follow-message {
    color: #ffb6c1;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(255, 105, 180, 0.3);
}

.follow-bubble .decorations {
    animation: followDecorationFloat 4s ease-in-out infinite;
}

@keyframes followFloat {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.7) rotate(-5deg);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 20, 147, 0.4);
    }
    3% {
        opacity: 0.6;
        transform: translateY(97vh) scale(0.8) rotate(-3deg);
        box-shadow: 0 10px 36px rgba(0, 0, 0, 0.45), 0 0 35px rgba(255, 20, 147, 0.5);
    }
    7% {
        opacity: 0.9;
        transform: translateY(93vh) scale(0.9) rotate(-1deg);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 0 40px rgba(255, 20, 147, 0.6);
    }
    12% {
        opacity: 1;
        transform: translateY(88vh) scale(1.02) rotate(0deg);
        box-shadow: 0 16px 48px rgba(0, 0, 0, 0.6), 0 0 50px rgba(255, 20, 147, 0.8);
    }
    88% {
        opacity: 1;
        transform: translateY(12vh) scale(1) rotate(0.5deg);
        box-shadow: 0 14px 44px rgba(0, 0, 0, 0.55), 0 0 45px rgba(255, 20, 147, 0.7);
    }
    93% {
        opacity: 0.8;
        transform: translateY(7vh) scale(0.95) rotate(1deg);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 0 40px rgba(255, 20, 147, 0.6);
    }
    97% {
        opacity: 0.5;
        transform: translateY(3vh) scale(0.85) rotate(1.5deg);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 20, 147, 0.4);
    }
    100% {
        opacity: 0;
        transform: translateY(-2vh) scale(0.7) rotate(2deg);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2), 0 0 20px rgba(255, 20, 147, 0.2);
    }
}

@keyframes followDecorationFloat {
    0%, 100% {
        transform: translateY(0px) rotate(0deg) scale(1);
    }
    20% {
        transform: translateY(-5px) rotate(8deg) scale(1.1);
    }
    40% {
        transform: translateY(-8px) rotate(15deg) scale(1.2);
    }
    60% {
        transform: translateY(-5px) rotate(-10deg) scale(1.15);
    }
    80% {
        transform: translateY(-10px) rotate(20deg) scale(1.25);
    }
}

/* Animation variants */
@keyframes floatUp {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.7) rotate(-5deg);
    }
    3% {
        opacity: 0.6;
        transform: translateY(97vh) scale(0.85) rotate(-3deg);
    }
    8% {
        opacity: 0.9;
        transform: translateY(92vh) scale(0.95) rotate(-1deg);
    }
    12% {
        opacity: 1;
        transform: translateY(88vh) scale(1) rotate(0deg);
    }
    88% {
        opacity: 1;
        transform: translateY(12vh) scale(1) rotate(0.5deg);
    }
    94% {
        opacity: 0.8;
        transform: translateY(6vh) scale(0.95) rotate(1deg);
    }
    97% {
        opacity: 0.4;
        transform: translateY(3vh) scale(0.9) rotate(1.5deg);
    }
    100% {
        opacity: 0;
        transform: translateY(-2vh) scale(0.8) rotate(2deg);
    }
}

@keyframes floatUpSlow {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.8);
    }
    5% {
        opacity: 0.7;
        transform: translateY(95vh) scale(0.9);
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(10vh) scale(1);
    }
    95% {
        opacity: 0.7;
        transform: translateY(5vh) scale(0.9);
    }
    100% {
        opacity: 0;
        transform: translateY(0vh) scale(0.8);
    }
}

@keyframes floatUpFast {
    0% {
        opacity: 0;
        transform: translateY(100vh) scale(0.8);
    }
    20% {
        opacity: 1;
        transform: translateY(80vh) scale(1);
    }
    80% {
        opacity: 1;
        transform: translateY(20vh) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-5vh) scale(0.8);
    }
}

.chat-bubble.slow { animation: floatUpSlow 18s ease-out forwards; }
.chat-bubble.fast { animation: floatUpFast 8s ease-out forwards; }



/* Settings Panel */
.settings-panel {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 3000;
    display: none;
    min-width: 250px;
}

.settings-panel h3 {
    margin-bottom: 15px;
    color: #ffd700;
}

.setting-group {
    margin-bottom: 15px;
}

.setting-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
}

.setting-group input[type="range"] {
    width: 100%;
    margin-bottom: 5px;
}

.setting-group select {
    width: 100%;
    padding: 5px;
    border-radius: 5px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: white;
}



button {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
}

button:hover {
    background: #5a6fd8;
}
