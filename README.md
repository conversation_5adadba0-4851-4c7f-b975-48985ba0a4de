# OBS Chat Floating Message Bubbles

A modern, animated chat bubble overlay system for OBS Studio that connects to your IndoFinity WebSocket server.

## Features

- 🎨 **Beautiful modern chat bubbles** with dark glass effect
- 👤 **Colorful avatar circles** with user initials
- 🌸 **Animated decorations** (flowers, stars, hearts)
- 🌈 **8 different avatar color schemes** that cycle automatically
- ⚙️ **Real-time settings panel** for customization
- 🔄 **Auto-reconnection** when WebSocket connection is lost
- 📱 **Responsive design** that works on any screen size
- 🎯 **OBS-ready** transparent background
- 🧪 **Demo mode** for testing and development
- ✨ **Smooth animations** with floating effects

## Setup Instructions

### 1. Files Setup
Make sure you have all these files in the same directory:
- `index.html` - Main HTML file for OBS
- `demo.html` - Demo version for testing
- `styles.css` - Styling and animations
- `script.js` - WebSocket connection and bubble logic
- `README.md` - This documentation

### 1.5. Quick Demo
Open `demo.html` in your browser to test the system:
- Press **T** for single test bubble
- Press **Space** for multiple bubbles
- Use demo control buttons
- Click ⚙️ for settings

### 2. OBS Studio Setup
1. Open OBS Studio
2. Add a new **Browser Source**
3. Set the URL to the full path of your `index.html` file:
   - Example: `file:///C:/Users/<USER>/Downloads/Des/index.html`
4. Set Width: `1920` and Height: `1080` (or your stream resolution)
5. Check ✅ **Shutdown source when not visible**
6. Check ✅ **Refresh browser when scene becomes active**

### 3. WebSocket Server
Make sure your IndoFinity WebSocket server is running on `ws://localhost:62024` and sending events in these formats:

**Chat Messages:**
```json
{
  "event": "chat",
  "data": {
    "uniqueId": "username",
    "comment": "chat message text"
  }
}
```

**Follow Events:**
```json
{
  "event": "follow",
  "data": {
    "uniqueId": "username"
  }
}
```

## Usage

### Settings Panel
Click the ⚙️ gear icon in the top-left corner to access settings:

- **Bubble Duration**: How long bubbles stay on screen (3-15 seconds)
- **Max Bubbles**: Maximum number of bubbles on screen at once (5-20)
- **Bubble Size**: Small, Medium, or Large bubble sizes
- **Animation Speed**: Slow, Medium, or Fast floating animation

### Testing
- Press the **T** key to generate test chat bubbles
- Press the **F** key to generate test follow bubbles
- Use the browser console:
  - `chatManager.testBubble()` for chat
  - `chatManager.testFollowBubble()` for follows

### Connection Status
- 🟢 **Connected**: Successfully connected to WebSocket
- 🔴 **Disconnected**: Connection lost (will auto-retry every 3-5 seconds)

## Customization

### Colors
The system uses 8 predefined gradient color schemes. To add more colors, edit the CSS file and add new `.color-X` classes.

### Animation Styles
Three animation speeds are available:
- **Slow**: 12 seconds duration
- **Medium**: 8 seconds duration  
- **Fast**: 5 seconds duration

### Bubble Positioning
Bubbles appear at random horizontal positions and float upward. The starting position is at the bottom of the screen.

## Troubleshooting

### WebSocket Connection Issues
1. Verify your WebSocket server is running on `localhost:62024`
2. Check browser console for error messages
3. Ensure the message format matches the expected JSON structure

### OBS Display Issues
1. Make sure the Browser Source URL is correct
2. Try refreshing the browser source
3. Check that Width/Height match your stream resolution
4. Verify "Shutdown source when not visible" is checked

### Performance
- Limit max bubbles to 10-15 for best performance
- Use shorter durations for high-traffic chats
- The system automatically removes old bubbles to prevent memory issues

## Browser Compatibility
- ✅ Chrome/Chromium (recommended for OBS)
- ✅ Firefox
- ✅ Edge
- ✅ Safari

## Development
The code is modular and easy to extend:
- `ChatBubbleManager` class handles all functionality
- Settings are stored in the `settings` object
- Easy to add new animation types or bubble styles

## License
Free to use and modify for personal and commercial projects.
