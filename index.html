<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OBS Chat Floating Bubbles</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="chat-container"></div>
    
    <!-- Settings Panel (Hidden by default) -->
    <div id="settings-panel" class="settings-panel">
        <h3>Chat Bubble Settings</h3>
        <div class="setting-group">
            <label for="bubble-duration">Bubble Duration (seconds):</label>
            <input type="range" id="bubble-duration" min="3" max="15" value="8">
            <span id="duration-value">8s</span>
        </div>
        <div class="setting-group">
            <label for="max-bubbles">Max Bubbles:</label>
            <input type="range" id="max-bubbles" min="5" max="20" value="10">
            <span id="max-bubbles-value">10</span>
        </div>
        <div class="setting-group">
            <label for="bubble-size">Bubble Size:</label>
            <select id="bubble-size">
                <option value="small">Small</option>
                <option value="medium" selected>Medium</option>
                <option value="large">Large</option>
            </select>
        </div>
        <div class="setting-group">
            <label for="animation-speed">Animation Speed:</label>
            <select id="animation-speed">
                <option value="slow">Slow</option>
                <option value="medium" selected>Medium</option>
                <option value="fast">Fast</option>
            </select>
        </div>
        <button id="toggle-settings">Hide Settings</button>
    </div>
    
    <button id="settings-toggle" class="settings-toggle">⚙️</button>
    
    <script src="script.js"></script>
</body>
</html>
