<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OBS Chat Floating Bubbles</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="chat-container"></div>

    <!-- Hidden settings elements for script compatibility -->
    <input type="range" id="bubble-duration" min="3" max="15" value="12" style="display: none;">
    <span id="duration-value" style="display: none;">12s</span>
    <input type="range" id="max-bubbles" min="5" max="20" value="10" style="display: none;">
    <span id="max-bubbles-value" style="display: none;">10</span>
    <select id="bubble-size" style="display: none;">
        <option value="small">Small</option>
        <option value="medium" selected>Medium</option>
        <option value="large">Large</option>
    </select>
    <select id="animation-speed" style="display: none;">
        <option value="slow">Slow</option>
        <option value="medium" selected>Medium</option>
        <option value="fast">Fast</option>
    </select>

    <script src="script.js"></script>
</body>
</html>
