<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Bubbles Demo</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }
        
        .demo-controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 15px;
            color: white;
            text-align: center;
            z-index: 5000;
        }
        
        .demo-controls button {
            margin: 5px;
            padding: 10px 20px;
            background: #ff6b9d;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .demo-controls button:hover {
            background: #ff5588;
        }
        
        .demo-info {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            max-width: 500px;
            z-index: 4000;
        }
        
        .demo-info h2 {
            color: #ff9ff3;
            margin-bottom: 15px;
        }
        
        .demo-info p {
            margin-bottom: 10px;
            line-height: 1.6;
        }
        
        .hide-info {
            display: none;
        }
    </style>
</head>
<body>
    <div id="chat-container"></div>
    <div id="connection-status" class="disconnected">Demo Mode</div>
    
    <div id="demo-info" class="demo-info">
        <h2>🎉 Chat Bubbles Demo</h2>
        <p>นี่คือการสาธิตระบบ Chat Bubbles สำหรับ OBS</p>
        <p><strong>การใช้งาน:</strong></p>
        <p>• กด <strong>T</strong> เพื่อสร้าง bubble ทดสอบ</p>
        <p>• กด <strong>Space</strong> เพื่อสร้าง bubble หลายๆ อัน</p>
        <p>• กดปุ่มด้านล่างเพื่อทดสอบ</p>
        <p>• กด ⚙️ เพื่อเปิดการตั้งค่า</p>
        <button onclick="hideInfo()">เริ่มทดสอบ</button>
    </div>
    
    <!-- Settings Panel -->
    <div id="settings-panel" class="settings-panel">
        <h3>Chat Bubble Settings</h3>
        <div class="setting-group">
            <label for="bubble-duration">Bubble Duration (seconds):</label>
            <input type="range" id="bubble-duration" min="3" max="15" value="8">
            <span id="duration-value">8s</span>
        </div>
        <div class="setting-group">
            <label for="max-bubbles">Max Bubbles:</label>
            <input type="range" id="max-bubbles" min="5" max="20" value="10">
            <span id="max-bubbles-value">10</span>
        </div>
        <div class="setting-group">
            <label for="bubble-size">Bubble Size:</label>
            <select id="bubble-size">
                <option value="small">Small</option>
                <option value="medium" selected>Medium</option>
                <option value="large">Large</option>
            </select>
        </div>
        <div class="setting-group">
            <label for="animation-speed">Animation Speed:</label>
            <select id="animation-speed">
                <option value="slow">Slow</option>
                <option value="medium" selected>Medium</option>
                <option value="fast">Fast</option>
            </select>
        </div>
        <button id="toggle-settings">Hide Settings</button>
    </div>
    
    <button id="settings-toggle" class="settings-toggle">⚙️</button>
    
    <div class="demo-controls">
        <h3>🎮 Demo Controls</h3>
        <button onclick="chatManager.testBubble()">Single Bubble</button>
        <button onclick="createMultipleBubbles()">Multiple Bubbles</button>
        <button onclick="createBurstBubbles()">Bubble Burst</button>
        <button onclick="clearAllBubbles()">Clear All</button>
    </div>
    
    <script src="script.js"></script>
    <script>
        // Demo-specific functions
        function hideInfo() {
            document.getElementById('demo-info').classList.add('hide-info');
        }
        
        function createMultipleBubbles() {
            const messages = [
                { user: 'Streamer', msg: 'สวัสดีทุกคน! 👋' },
                { user: 'Fan1', msg: 'เย้ๆๆ มาแล้ว! 🎉' },
                { user: 'Viewer123', msg: 'สตรีมสนุกมาก! 😍' },
                { user: 'ChatMaster', msg: 'ขอบคุณสำหรับสตรีมนะ 💖' },
                { user: 'GamerPro', msg: 'เล่นเก่งมาก! 🔥' }
            ];
            
            messages.forEach((msg, index) => {
                setTimeout(() => {
                    chatManager.createChatBubble(msg.user, msg.msg);
                }, index * 500);
            });
        }
        
        function createBurstBubbles() {
            const burstMessages = [
                'Amazing! 🌟', 'Wow! 😱', 'Cool! 😎', 'Nice! 👍', 
                'Perfect! 💯', 'Great! 🎊', 'Awesome! 🚀', 'Epic! ⚡'
            ];
            
            for (let i = 0; i < 8; i++) {
                setTimeout(() => {
                    const randomMsg = burstMessages[Math.floor(Math.random() * burstMessages.length)];
                    chatManager.createChatBubble(`User${i + 1}`, randomMsg);
                }, i * 200);
            }
        }
        
        function clearAllBubbles() {
            chatManager.activeBubbles.forEach(bubble => {
                if (bubble && bubble.parentNode) {
                    bubble.remove();
                }
            });
            chatManager.activeBubbles = [];
        }
        
        // Additional keyboard shortcuts for demo
        document.addEventListener('keydown', (e) => {
            if (e.key === ' ' && !e.ctrlKey && !e.altKey) {
                e.preventDefault();
                createMultipleBubbles();
            }
        });
        
        // Auto-hide info after 10 seconds
        setTimeout(() => {
            hideInfo();
        }, 10000);
    </script>
</body>
</html>
